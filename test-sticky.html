<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>测试右侧跟随滚动</title>
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }
        
        .container {
            width: 1240px;
            margin: 0 auto;
            background-color: white;
            min-height: 3000px;
            padding: 20px;
            display: flex;
        }
        
        .main-content {
            flex: 1;
            margin-right: 20px;
        }
        
        .sidebar {
            width: 260px;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
        }
        
        .float1, .float2 {
            width: 260px;
            height: 140px;
            background-color: #3397E9;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .news-ad {
            width: 260px;
            height: 140px;
            background-color: #ff6b6b;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .content-block {
            height: 300px;
            background-color: #e9ecef;
            margin-bottom: 20px;
            padding: 20px;
            border-radius: 8px;
        }
        
        .footer {
            background-color: #2f3542;
            color: #fff;
            padding: 40px 20px;
            text-align: center;
            margin-top: 50px;
        }
        
        /* 引入sticky.css的样式 */
        .right-sidebar-sticky-container {
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            z-index: 99 !important;
        }
        
        .right-sidebar-sticky-container .float1,
        .right-sidebar-sticky-container .float2,
        .right-sidebar-sticky-container .news-ad {
            margin-bottom: 20px;
            width: 260px !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="main-content">
            <h1>主要内容区域</h1>
            <div class="content-block">
                <h2>内容块 1</h2>
                <p>这里是一些测试内容，用来模拟页面的主要内容区域。</p>
            </div>
            <div class="content-block">
                <h2>内容块 2</h2>
                <p>继续滚动查看右侧元素的跟随效果。</p>
            </div>
            <div class="content-block">
                <h2>内容块 3</h2>
                <p>当页面滚动到footer附近时，右侧元素应该停留在当前位置。</p>
            </div>
            <div class="content-block">
                <h2>内容块 4</h2>
                <p>更多内容...</p>
            </div>
            <div class="content-block">
                <h2>内容块 5</h2>
                <p>接近footer了...</p>
            </div>
        </div>
        
        <div class="sidebar">
            <div class="float1">Float1 元素</div>
            <div class="float2">Float2 元素</div>
            <div class="news-ad">News Ad 1</div>
            <div class="news-ad">News Ad 2</div>
        </div>
    </div>
    
    <div class="footer">
        <h3>Footer 区域</h3>
        <p>这是页面的footer部分。右侧跟随滚动的元素应该在碰到这里时停止跟随。</p>
    </div>
    
    <script src="/public/js/sticky.js"></script>
</body>
</html>
